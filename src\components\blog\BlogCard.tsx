'use client';

import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, User, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { useTheme } from '@/hooks/useTheme';

interface BlogPost {
  id: string;
  _id: string;
  title: string;
  excerpt: string;
  slug: string;
  featuredImage?: string;
  imageCredit?: string;
  categories: string[];
  tags: string[];
  publishedAt: string;
  author: {
    name: string;
    email: string;
  };
}

interface BlogCardProps {
  post: BlogPost;
  index: number;
}

export function BlogCard({ post, index }: BlogCardProps) {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Category color mapping for badges
  const getCategoryColor = (category: string) => {
    const colors = {
      'Technology': isDark ? 'bg-blue-500/20 text-blue-300 border-blue-500/30' : 'bg-blue-100 text-blue-800 border-blue-200',
      'Food': isDark ? 'bg-green-500/20 text-green-300 border-green-500/30' : 'bg-green-100 text-green-800 border-green-200',
      'Automotive': isDark ? 'bg-purple-500/20 text-purple-300 border-purple-500/30' : 'bg-purple-100 text-purple-800 border-purple-200',
      'General': isDark ? 'bg-orange-500/20 text-orange-300 border-orange-500/30' : 'bg-orange-100 text-orange-800 border-orange-200',
      'Design': isDark ? 'bg-violet-500/20 text-violet-300 border-violet-500/30' : 'bg-violet-100 text-violet-800 border-violet-200',
      'Productivity': isDark ? 'bg-emerald-500/20 text-emerald-300 border-emerald-500/30' : 'bg-emerald-100 text-emerald-800 border-emerald-200',
      'Web': isDark ? 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30' : 'bg-cyan-100 text-cyan-800 border-cyan-200',
      'AI': isDark ? 'bg-pink-500/20 text-pink-300 border-pink-500/30' : 'bg-pink-100 text-pink-800 border-pink-200',
    };
    return colors[category as keyof typeof colors] || (isDark ? 'bg-gray-500/20 text-gray-300 border-gray-500/30' : 'bg-gray-100 text-gray-800 border-gray-200');
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.6,
        delay: index * 0.1,
        ease: "easeOut",
        type: "spring",
        stiffness: 100
      }}
      whileHover={{
        y: -12,
        scale: 1.02,
        transition: { duration: 0.3, ease: "easeOut" }
      }}
      whileTap={{ scale: 0.98 }}
      className="group h-full"
    >
      <div className={`
        h-full rounded-3xl overflow-hidden shadow-lg transition-all duration-500 group-hover:shadow-2xl
        ${isDark
          ? 'bg-gradient-to-br from-gray-900/90 via-gray-800/90 to-gray-900/90 border border-gray-700/30 group-hover:border-gray-600/50 backdrop-blur-sm'
          : 'bg-gradient-to-br from-white via-gray-50/50 to-white border border-gray-200/50 group-hover:border-gray-300/70 backdrop-blur-sm'
        }
        group-hover:shadow-primary/10
      `}>
        {/* Enhanced Image Section */}
        <div className="relative h-52 overflow-hidden rounded-t-3xl">
          <motion.img
            src={post.featuredImage || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80'}
            alt={post.title}
            className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
            whileHover={{ scale: 1.05 }}
          />

          {/* Enhanced Category Badge */}
          <motion.div
            className="absolute top-4 left-4"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <Badge
              className={`
                ${getCategoryColor(post.categories[0] || 'General')}
                border font-semibold px-4 py-2 text-xs rounded-full shadow-lg backdrop-blur-md
                transition-all duration-300 hover:shadow-xl
              `}
            >
              {post.categories[0] || 'General'}
            </Badge>
          </motion.div>

          {/* Enhanced Image Credit */}
          {post.imageCredit && (
            <motion.div
              className="absolute bottom-3 right-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              <span className="text-xs text-white/80 bg-black/60 px-3 py-1.5 rounded-full backdrop-blur-md font-medium">
                📸 {post.imageCredit}
              </span>
            </motion.div>
          )}

          {/* Enhanced Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500" />

          {/* Shimmer Effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100"
            animate={{
              x: ['-100%', '100%'],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatDelay: 3,
            }}
          />
        </div>

        {/* Enhanced Content Section */}
        <div className="p-7 flex flex-col h-[calc(100%-13rem)]">
          {/* Enhanced Meta Information */}
          <motion.div
            className="flex items-center gap-4 text-sm text-muted-foreground mb-4"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span className="font-medium">{formatDate(post.publishedAt)}</span>
            </div>
            <div className="w-1 h-1 bg-muted-foreground rounded-full" />
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span className="font-medium">{post.author.name}</span>
            </div>
          </motion.div>

          {/* Enhanced Title */}
          <motion.h3
            className={`
              text-xl font-bold mb-4 line-clamp-2 group-hover:text-primary transition-all duration-300
              ${isDark ? 'text-gray-100' : 'text-gray-900'}
              leading-tight
            `}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            {post.title}
          </motion.h3>

          {/* Enhanced Excerpt */}
          <motion.p
            className={`
              text-sm mb-6 line-clamp-3 flex-grow leading-relaxed
              ${isDark ? 'text-gray-300' : 'text-gray-600'}
            `}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            {post.excerpt}
          </motion.p>

          {/* Enhanced Read More Button */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <Link href={`/blog/${post.slug}`}>
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="w-full"
              >
                <Button
                  variant="ghost"
                  className={`
                    w-full group/btn justify-between px-4 py-3 rounded-xl font-semibold text-sm
                    transition-all duration-300 hover:shadow-md
                    ${isDark
                      ? 'text-gray-300 hover:text-white hover:bg-gray-800/50 border border-gray-700/50 hover:border-gray-600/50'
                      : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100/50 border border-gray-200/50 hover:border-gray-300/50'
                    }
                  `}
                >
                  <span>Read More</span>
                  <motion.div
                    whileHover={{ x: 6 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                  >
                    <ArrowRight className="h-4 w-4" />
                  </motion.div>
                </Button>
              </motion.div>
            </Link>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}

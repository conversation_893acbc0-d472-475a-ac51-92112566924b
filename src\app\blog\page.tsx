'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { BlogHero } from '@/components/blog/BlogHero';
import { BlogCard } from '@/components/blog/BlogCard';
import { FeaturedTools } from '@/components/blog/FeaturedTools';
import { TrendingTopics } from '@/components/blog/TrendingTopics';
import { AboutPlatform } from '@/components/blog/AboutPlatform';
import { fetchBlogPosts } from '@/services/blogService';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BlogPost {
  id: string;
  _id: string;
  title: string;
  excerpt: string;
  content: string;
  slug: string;
  featuredImage?: string;
  imageCredit?: string;
  categories: string[];
  tags: string[];
  publishedAt: string;
  author: {
    name: string;
    email: string;
  };
}

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const postsPerPage = 9; // 3x3 grid

  useEffect(() => {
    loadPosts();
  }, [currentPage]);

  const loadPosts = async () => {
    try {
      setLoading(true);
      const response = await fetchBlogPosts({
        page: currentPage,
        limit: postsPerPage,
        status: 'published'
      });

      if (response.success && response.data) {
        setPosts(response.data);
        if (response.pagination) {
          setTotalPages(response.pagination.totalPages);
        }
      }
    } catch (error) {
      console.error('Error loading posts:', error);
      // Fallback to mock data if API fails
      const mockPosts = generateMockPosts();
      setPosts(mockPosts);
      setTotalPages(Math.ceil(mockPosts.length / postsPerPage));
    } finally {
      setLoading(false);
    }
  };

  const generateMockPosts = (): BlogPost[] => {
    return [
      {
        id: "1",
        _id: "1",
        title: "What's New In 2024 Tech",
        excerpt: "Discover the latest technological innovations and trends that are shaping the digital landscape in 2024.",
        content: "Technology continues to evolve...",
        slug: "whats-new-2024-tech",
        featuredImage: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&q=80",
        imageCredit: "Unsplash",
        categories: ["Technology"],
        tags: ["Tech", "Innovation", "2024"],
        publishedAt: "2024-01-15",
        author: { name: "Jane Doe", email: "<EMAIL>" }
      },
      {
        id: "2",
        _id: "2",
        title: "Delicious Food Trends",
        excerpt: "Explore the culinary innovations and food trends that are taking the world by storm this year.",
        content: "Food culture is constantly evolving...",
        slug: "delicious-food-trends",
        featuredImage: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&q=80",
        imageCredit: "Unsplash",
        categories: ["Food"],
        tags: ["Food", "Trends", "Culinary"],
        publishedAt: "2024-01-10",
        author: { name: "John Doe", email: "<EMAIL>" }
      },
      {
        id: "3",
        _id: "3",
        title: "Race To Your Heart Content",
        excerpt: "Discover how automotive technology is revolutionizing the way we think about transportation and mobility.",
        content: "The automotive industry is experiencing...",
        slug: "race-to-your-heart-content",
        featuredImage: "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&q=80",
        imageCredit: "Unsplash",
        categories: ["Automotive"],
        tags: ["Cars", "Technology", "Innovation"],
        publishedAt: "2024-01-05",
        author: { name: "John Doe", email: "<EMAIL>" }
      },
      // Add more mock posts to fill the grid
      ...Array.from({ length: 6 }, (_, i) => ({
        id: `${i + 4}`,
        _id: `${i + 4}`,
        title: `Amazing Article ${i + 4}`,
        excerpt: `This is an amazing article about various topics that will interest you and provide valuable insights.`,
        content: "Content here...",
        slug: `amazing-article-${i + 4}`,
        featuredImage: `https://images.unsplash.com/photo-${1500000000000 + i}?w=800&q=80`,
        imageCredit: "Unsplash",
        categories: ["General"],
        tags: ["Article", "Content"],
        publishedAt: `2024-01-${String(i + 1).padStart(2, '0')}`,
        author: { name: "Author Name", email: "<EMAIL>" }
      }))
    ];
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Hero Section */}
      <BlogHero />

      {/* Enhanced Blog Cards Grid Section */}
      <section className="py-20 bg-background relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, currentColor 1px, transparent 0)`,
            backgroundSize: '20px 20px'
          }} />
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
              >
                ✨
              </motion.div>
              Latest Articles
            </motion.div>

            <motion.h2
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary via-purple-500 to-orange-500 bg-clip-text text-transparent"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Discover Amazing Stories
            </motion.h2>

            <motion.p
              className="text-muted-foreground text-xl max-w-3xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              Explore our collection of insightful articles, tutorials, and stories that inspire creativity and boost productivity
            </motion.p>
          </motion.div>

          {loading ? (
            <motion.div
              className="text-center py-20"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <motion.div
                className="inline-block h-12 w-12 rounded-full border-4 border-primary/20 border-t-primary"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
              <motion.p
                className="mt-6 text-muted-foreground text-lg"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                Loading amazing articles...
              </motion.p>
            </motion.div>
          ) : (
            <>
              {/* Enhanced Blog Cards Grid */}
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10 mb-16"
              >
                {posts.map((post, index) => (
                  <motion.div
                    key={post.id}
                    variants={itemVariants}
                    className="h-full"
                  >
                    <BlogCard post={post} index={index} />
                  </motion.div>
                ))}
              </motion.div>

              {/* Enhanced Pagination */}
              {totalPages > 1 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="flex justify-center items-center gap-6"
                >
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="flex items-center gap-3 px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="h-5 w-5" />
                      Previous
                    </Button>
                  </motion.div>

                  <div className="flex gap-3">
                    {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                      let page;
                      if (totalPages <= 5) {
                        page = i + 1;
                      } else if (currentPage <= 3) {
                        page = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        page = totalPages - 4 + i;
                      } else {
                        page = currentPage - 2 + i;
                      }

                      return (
                        <motion.div
                          key={page}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Button
                            variant={currentPage === page ? "default" : "outline"}
                            onClick={() => setCurrentPage(page)}
                            className={`
                              w-12 h-12 rounded-full font-bold transition-all duration-300
                              ${currentPage === page
                                ? 'bg-gradient-to-r from-primary to-purple-500 text-white shadow-lg shadow-primary/25'
                                : 'hover:shadow-md'
                              }
                            `}
                          >
                            {page}
                          </Button>
                        </motion.div>
                      );
                    })}
                  </div>

                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className="flex items-center gap-3 px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                      <ChevronRight className="h-5 w-5" />
                    </Button>
                  </motion.div>
                </motion.div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Featured Tools Section */}
      <FeaturedTools />

      {/* Trending Topics Slider */}
      <TrendingTopics />

      {/* About Platform Section */}
      <AboutPlatform />

      <Footer />
    </div>
  );
}
